/**
 * 推币机游戏后端服务主应用文件
 * 负责初始化Express应用、配置中间件、路由和WebSocket服务
 * <AUTHOR> Team
 * @version 1.0.0
 */

const express = require('express');
const http = require('http');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');

// 导入配置和工具
const config = require('./config');
const logger = require('./utils/logger');
const { STRINGS } = require('./constants/strings');

// 导入数据库连接
const dbConnection = require('./database/connection');
const redisClient = require('./database/redis');

// 导入中间件
const {
  securityMiddleware,
  corsMiddleware,
  compressionMiddleware,
  requestLoggerMiddleware,
  rateLimitMiddleware,
  errorHandler
} = require('./middleware');

// 导入路由
const routes = require('./routes');

// 导入WebSocket服务
const createWebSocketServer = require('./websocket');

/**
 * 创建Express应用实例
 * @returns {Object} Express应用实例
 */
function createApp() {
  const app = express();

  // 设置信任代理（用于获取真实IP）
  app.set('trust proxy', config.app.trustProxy);

  // 基础中间件
  app.use(helmet(config.security.helmet));
  app.use(cors(config.cors));
  app.use(compression());
  app.use(express.json({ limit: config.app.jsonLimit }));
  app.use(express.urlencoded({ extended: true, limit: config.app.urlencodedLimit }));

  // 请求日志中间件
  app.use(requestLoggerMiddleware);

  // 限流中间件
  app.use(rateLimitMiddleware);

  // 静态文件服务（如果需要）
  if (config.app.serveStatic) {
    app.use('/static', express.static(path.join(__dirname, '../public')));
  }

  // 健康检查端点
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: STRINGS.HTTP_STATUS.SUCCESS,
      message: STRINGS.SYSTEM.HEALTH_CHECK_OK,
      timestamp: new Date().toISOString(),
      version: config.app.version
    });
  });

  // API路由
  app.use('/api', routes);

  // 404处理
  app.use('*', (req, res) => {
    res.status(404).json({
      status: STRINGS.HTTP_STATUS.ERROR,
      message: STRINGS.HTTP_STATUS.NOT_FOUND,
      path: req.originalUrl
    });
  });

  // 错误处理中间件
  app.use(errorHandler);

  return app;
}

/**
 * 初始化数据库连接
 * @returns {Promise<void>}
 */
async function initializeDatabase() {
  try {
    // 初始化MySQL连接池
    await dbConnection.createPool();
    logger.info(STRINGS.LOG.DATABASE_CONNECTED);

    // 初始化Redis连接
    await redisClient.createConnection();
    logger.info(STRINGS.LOG.REDIS_CONNECTED);

    // 测试数据库连接
    const dbStatus = await dbConnection.checkConnection();
    const redisStatus = await redisClient.checkConnection();

    if (!dbStatus || !redisStatus) {
      throw new Error(STRINGS.ERROR.DATABASE_CONNECTION_FAILED);
    }

    logger.info(STRINGS.LOG.DATABASE_INITIALIZED);
  } catch (error) {
    logger.error(STRINGS.ERROR.DATABASE_INIT_FAILED, { error: error.message });
    throw error;
  }
}

/**
 * 启动服务器
 * @returns {Promise<Object>} 服务器实例和WebSocket服务器
 */
async function startServer() {
  try {
    // 初始化数据库
    await initializeDatabase();

    // 创建Express应用
    const app = createApp();

    // 创建HTTP服务器
    const server = http.createServer(app);

    // 创建WebSocket服务器
    const io = createWebSocketServer(server);

    // 启动服务器
    const port = config.app.port;
    server.listen(port, config.app.host, () => {
      logger.info(STRINGS.LOG.SERVER_STARTED, {
        host: config.app.host,
        port: port,
        env: config.app.env,
        version: config.app.version
      });
    });

    // 优雅关闭处理
    const gracefulShutdown = async (signal) => {
      logger.info(STRINGS.LOG.SERVER_SHUTTING_DOWN, { signal });

      // 停止接受新连接
      server.close(async () => {
        try {
          // 关闭WebSocket连接
          io.close();

          // 先关闭Redis连接
          await redisClient.disconnect();

          // 再关闭数据库连接
          await dbConnection.closePool();

          logger.info(STRINGS.LOG.SERVER_SHUTDOWN_COMPLETE);
          process.exit(0);
        } catch (error) {
          logger.error(STRINGS.ERROR.SHUTDOWN_ERROR, { error: error.message });
          process.exit(1);
        }
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error(STRINGS.ERROR.SHUTDOWN_TIMEOUT);
        process.exit(1);
      }, config.app.shutdownTimeout);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 监听未捕获的异常
    process.on('uncaughtException', (error) => {
      logger.error(STRINGS.ERROR.UNCAUGHT_EXCEPTION, { error: error.message, stack: error.stack });
      gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error(STRINGS.ERROR.UNHANDLED_REJECTION, { reason, promise });
      gracefulShutdown('unhandledRejection');
    });

    return { server, io, app };
  } catch (error) {
    logger.error(STRINGS.ERROR.SERVER_START_FAILED, { error: error.message });
    process.exit(1);
  }
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer().catch((error) => {
    logger.error(STRINGS.ERROR.STARTUP_ERROR, { error: error.message });
    process.exit(1);
  });
}

module.exports = {
  createApp,
  startServer,
  initializeDatabase
};